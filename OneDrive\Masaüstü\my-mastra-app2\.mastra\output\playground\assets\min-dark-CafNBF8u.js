const e=Object.freeze(JSON.parse('{"colors":{"activityBar.background":"#1A1A1A","activityBar.foreground":"#7D7D7D","activityBarBadge.background":"#383838","badge.background":"#383838","badge.foreground":"#C1C1C1","button.background":"#333","debugIcon.breakpointCurrentStackframeForeground":"#79b8ff","debugIcon.breakpointDisabledForeground":"#848484","debugIcon.breakpointForeground":"#FF7A84","debugIcon.breakpointStackframeForeground":"#79b8ff","debugIcon.breakpointUnverifiedForeground":"#848484","debugIcon.continueForeground":"#FF7A84","debugIcon.disconnectForeground":"#FF7A84","debugIcon.pauseForeground":"#FF7A84","debugIcon.restartForeground":"#79b8ff","debugIcon.startForeground":"#79b8ff","debugIcon.stepBackForeground":"#FF7A84","debugIcon.stepIntoForeground":"#FF7A84","debugIcon.stepOutForeground":"#FF7A84","debugIcon.stepOverForeground":"#FF7A84","debugIcon.stopForeground":"#79b8ff","diffEditor.insertedTextBackground":"#3a632a4b","diffEditor.removedTextBackground":"#88063852","editor.background":"#1f1f1f","editor.lineHighlightBorder":"#303030","editorGroupHeader.tabsBackground":"#1A1A1A","editorGroupHeader.tabsBorder":"#1A1A1A","editorIndentGuide.activeBackground":"#383838","editorIndentGuide.background":"#2A2A2A","editorLineNumber.foreground":"#727272","editorRuler.foreground":"#2A2A2A","editorSuggestWidget.background":"#1A1A1A","focusBorder":"#444","foreground":"#888888","gitDecoration.ignoredResourceForeground":"#444444","input.background":"#2A2A2A","input.foreground":"#E0E0E0","inputOption.activeBackground":"#3a3a3a","list.activeSelectionBackground":"#212121","list.activeSelectionForeground":"#F5F5F5","list.focusBackground":"#292929","list.highlightForeground":"#EAEAEA","list.hoverBackground":"#262626","list.hoverForeground":"#9E9E9E","list.inactiveSelectionBackground":"#212121","list.inactiveSelectionForeground":"#F5F5F5","panelTitle.activeBorder":"#1f1f1f","panelTitle.activeForeground":"#FAFAFA","panelTitle.inactiveForeground":"#484848","peekView.border":"#444","peekViewEditor.background":"#242424","pickerGroup.border":"#363636","pickerGroup.foreground":"#EAEAEA","progressBar.background":"#FAFAFA","scrollbar.shadow":"#1f1f1f","sideBar.background":"#1A1A1A","sideBarSectionHeader.background":"#202020","statusBar.background":"#1A1A1A","statusBar.debuggingBackground":"#1A1A1A","statusBar.foreground":"#7E7E7E","statusBar.noFolderBackground":"#1A1A1A","statusBarItem.prominentBackground":"#fafafa1a","statusBarItem.remoteBackground":"#1a1a1a00","statusBarItem.remoteForeground":"#7E7E7E","symbolIcon.classForeground":"#FF9800","symbolIcon.constructorForeground":"#b392f0","symbolIcon.enumeratorForeground":"#FF9800","symbolIcon.enumeratorMemberForeground":"#79b8ff","symbolIcon.eventForeground":"#FF9800","symbolIcon.fieldForeground":"#79b8ff","symbolIcon.functionForeground":"#b392f0","symbolIcon.interfaceForeground":"#79b8ff","symbolIcon.methodForeground":"#b392f0","symbolIcon.variableForeground":"#79b8ff","tab.activeBorder":"#1e1e1e","tab.activeForeground":"#FAFAFA","tab.border":"#1A1A1A","tab.inactiveBackground":"#1A1A1A","tab.inactiveForeground":"#727272","terminal.ansiBrightBlack":"#5c5c5c","textLink.activeForeground":"#fafafa","textLink.foreground":"#CCC","titleBar.activeBackground":"#1A1A1A","titleBar.border":"#00000000"},"displayName":"Min Dark","name":"min-dark","semanticHighlighting":true,"tokenColors":[{"settings":{"foreground":"#b392f0"}},{"scope":["support.function","keyword.operator.accessor","meta.group.braces.round.function.arguments","meta.template.expression","markup.fenced_code meta.embedded.block"],"settings":{"foreground":"#b392f0"}},{"scope":"emphasis","settings":{"fontStyle":"italic"}},{"scope":["strong","markup.heading.markdown","markup.bold.markdown"],"settings":{"fontStyle":"bold","foreground":"#FF7A84"}},{"scope":["markup.italic.markdown"],"settings":{"fontStyle":"italic"}},{"scope":"meta.link.inline.markdown","settings":{"fontStyle":"underline","foreground":"#1976D2"}},{"scope":["string","markup.fenced_code","markup.inline"],"settings":{"foreground":"#9db1c5"}},{"scope":["comment","string.quoted.docstring.multi"],"settings":{"foreground":"#6b737c"}},{"scope":["constant.language","variable.language.this","variable.other.object","variable.other.class","variable.other.constant","meta.property-name","support","string.other.link.title.markdown"],"settings":{"foreground":"#79b8ff"}},{"scope":["constant.numeric","constant.other.placeholder","constant.character.format.placeholder","meta.property-value","keyword.other.unit","keyword.other.template","entity.name.tag.yaml","entity.other.attribute-name","support.type.property-name.json"],"settings":{"foreground":"#f8f8f8"}},{"scope":["keyword","storage.modifier","storage.type","storage.control.clojure","entity.name.function.clojure","support.function.node","punctuation.separator.key-value","punctuation.definition.template-expression"],"settings":{"foreground":"#f97583"}},{"scope":"variable.parameter.function","settings":{"foreground":"#FF9800"}},{"scope":["entity.name.type","entity.other.inherited-class","meta.function-call","meta.instance.constructor","entity.other.attribute-name","entity.name.function","constant.keyword.clojure"],"settings":{"foreground":"#b392f0"}},{"scope":["entity.name.tag","string.quoted","string.regexp","string.interpolated","string.template","string.unquoted.plain.out.yaml","keyword.other.template"],"settings":{"foreground":"#ffab70"}},{"scope":"token.info-token","settings":{"foreground":"#316bcd"}},{"scope":"token.warn-token","settings":{"foreground":"#cd9731"}},{"scope":"token.error-token","settings":{"foreground":"#cd3131"}},{"scope":"token.debug-token","settings":{"foreground":"#800080"}},{"scope":["punctuation.definition.arguments","punctuation.definition.dict","punctuation.separator","meta.function-call.arguments"],"settings":{"foreground":"#bbbbbb"}},{"scope":"markup.underline.link","settings":{"foreground":"#ffab70"}},{"scope":["beginning.punctuation.definition.list.markdown"],"settings":{"foreground":"#FF7A84"}},{"scope":"punctuation.definition.metadata.markdown","settings":{"foreground":"#ffab70"}},{"scope":["punctuation.definition.string.begin.markdown","punctuation.definition.string.end.markdown"],"settings":{"foreground":"#79b8ff"}}],"type":"dark"}'));export{e as default};
