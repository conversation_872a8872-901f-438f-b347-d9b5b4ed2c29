import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { MCPClient } from '@mastra/mcp';

// MCP Client'ı oluştur ve debug ekle
const mcpClient = new MCPClient({
  servers: {
    smithery: {
      type: "http",
      url: "https://server.smithery.ai/@meren41/s-navmobil/mcp?api_key=02050232-0fc4-4ada-91d7-aa175ae658a7",
    },
  },
});

// MCP client'ı test et
async function testMCPClient() {
  try {
    console.log('🔍 MCP Client test ediliyor...');
    const tools = await mcpClient.listTools();
    console.log('✅ MCP Tools bulundu:', tools);
    return tools;
  } catch (error) {
    console.error('❌ MCP Client hatası:', error);
    return [];
  }
}

// Test et
testMCPClient();

// Agent'ı oluştur
export const myAgent = new Agent({
  name: 'MCP Chat Agent',
  instructions: `Sen bir şaka robotu asistanısın. Her mesajda MUTLAKA get_random_joke tool'unu kullanmalısın.

  ZORUNLU KURALLAR:
  1. HER MESAJDA get_random_joke tool'unu çağır - İSTİSNA YOK!
  2. Önce tool'u çağır, sonra yanıt ver
  3. Kullanıcı ne derse desin, her zaman bir şaka getir
  4. Tool çağırmadan asla yanıt verme

  TOOL KULLANIMI:
  - Tool adı: get_random_joke
  - Parametre: {} (boş obje)
  - Her mesajda MUTLAKA kullan

  YANIT FORMATI:
  1. get_random_joke tool'unu çağır
  2. Şakayı kullanıcıya sun
  3. Kullanıcının sorusunu yanıtla

  UNUTMA: HER MESAJDA TOOL KULLAN!`,
  model: openai('gpt-4o-mini'),
  mcpClient,
});
