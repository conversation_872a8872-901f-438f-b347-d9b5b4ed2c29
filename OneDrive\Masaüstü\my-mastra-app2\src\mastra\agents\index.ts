import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';

// Şimdilik MCP client olmadan agent oluşturalım ve manual tool ekleyelim
console.log('🚀 Agent oluşturuluyor...');

// Manual tool tanımla
const getRandomJokeTool = {
  name: 'get_random_joke',
  description: 'Rastgele bir şaka getirir',
  parameters: {
    type: 'object',
    properties: {},
    required: []
  },
  execute: async () => {
    try {
      console.log('🎭 get_random_joke tool çağrılıyor...');

      // Smithery API'sini doğru formatla çağır
      const response = await fetch('https://server.smithery.ai/@meren41/s-navmobil/mcp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer 02050232-0fc4-4ada-91d7-aa175ae658a7',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: Math.floor(Math.random() * 1000),
          method: 'tools/call',
          params: {
            name: 'get_random_joke',
            arguments: {}
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('🎭 MCP API yanıtı:', JSON.stringify(data, null, 2));

      // MCP response formatını kontrol et
      if (data.result) {
        if (data.result.content) {
          // Content array ise
          if (Array.isArray(data.result.content)) {
            const textContent = data.result.content.find(c => c.type === 'text');
            if (textContent) {
              console.log('✅ Şaka bulundu:', textContent.text);
              return textContent.text;
            }
          }
          // Content string ise
          if (typeof data.result.content === 'string') {
            console.log('✅ Şaka bulundu:', data.result.content);
            return data.result.content;
          }
        }

        // Result direkt string ise
        if (typeof data.result === 'string') {
          console.log('✅ Şaka bulundu:', data.result);
          return data.result;
        }
      }

      console.log('⚠️ Beklenmeyen response formatı, fallback şaka kullanılıyor');
      return 'İşte size bir şaka: Neden bilgisayarlar soğuk algınlığına yakalanmaz? Çünkü Windows açık bırakmazlar! 😄';

    } catch (error) {
      console.error('❌ MCP API hatası:', error);
      return 'İşte size bir şaka: Programcı neden merdiven çıkmaz? Çünkü her zaman elevator kullanır! 😄';
    }
  }
};

// Agent'ı oluştur
export const myAgent = new Agent({
  name: 'MCP Chat Agent',
  instructions: `Sen bir şaka robotu asistanısın. Her mesajda MUTLAKA get_random_joke tool'unu kullanmalısın.

  ZORUNLU KURALLAR:
  1. HER MESAJDA get_random_joke tool'unu çağır - İSTİSNA YOK!
  2. Önce tool'u çağır, sonra yanıt ver
  3. Kullanıcı ne derse desin, her zaman bir şaka getir
  4. Tool çağırmadan asla yanıt verme

  TOOL KULLANIMI:
  - Tool adı: get_random_joke
  - Parametre: {} (boş obje)
  - Her mesajda MUTLAKA kullan

  YANIT FORMATI:
  1. get_random_joke tool'unu çağır
  2. Şakayı kullanıcıya sun
  3. Kullanıcının sorusunu yanıtla

  UNUTMA: HER MESAJDA TOOL KULLAN!`,
  model: openai('gpt-4o-mini'),
  tools: [getRandomJokeTool],
});
