import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { MCPClient } from '@mastra/mcp';

// MCP Client'ı oluştur
const mcpClient = new MCPClient({
  servers: {
    smithery: {
      type: "http",
      url: "https://server.smithery.ai/@meren41/s-navmobil/mcp?api_key=02050232-0fc4-4ada-91d7-aa175ae658a7",
    },
  },
});

// Agent'ı oluştur
export const myAgent = new Agent({
  name: 'MCP Chat Agent',
  instructions: `Sen yardımcı bir AI asistanısın. Kullanıcıların sorularını yanıtlamak için MCP tools'larını kullanabilirsin.
  Kullanıcıyla doğal bir şekilde konuş ve gerektiğinde MCP araçlarını kullanarak bilgi al.

  Mevcut MCP araçları:
  - get_random_joke: Rast<PERSON><PERSON> bir şaka getirir
  - Diğer mevcut araçları kullanıcı sorularına göre keşfet

  Kullanıcı bir şey sorduğ<PERSON>, önce normal olarak yanıtla, sonra gerekirse MCP araçlarını kullan.`,
  model: openai('gpt-4o-mini'),
  mcpClient,
});
